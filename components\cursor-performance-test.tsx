"use client"

import { useEffect, useRef, useState } from "react"

interface PerformanceMetrics {
  fps: number
  frameTime: number
  memoryUsage: number
}

export function CursorPerformanceTest() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 0,
    frameTime: 0,
    memoryUsage: 0
  })
  const [isVisible, setIsVisible] = useState(false)
  const frameCountRef = useRef(0)
  const lastTimeRef = useRef(performance.now())
  const animationFrameRef = useRef<number>()

  const measurePerformance = () => {
    const now = performance.now()
    const deltaTime = now - lastTimeRef.current
    frameCountRef.current++

    // Update metrics every second
    if (deltaTime >= 1000) {
      const fps = Math.round((frameCountRef.current * 1000) / deltaTime)
      const frameTime = deltaTime / frameCountRef.current
      
      // Get memory usage if available
      const memoryUsage = (performance as any).memory 
        ? Math.round((performance as any).memory.usedJSHeapSize / 1024 / 1024)
        : 0

      setMetrics({
        fps,
        frameTime: Math.round(frameTime * 100) / 100,
        memoryUsage
      })

      frameCountRef.current = 0
      lastTimeRef.current = now
    }

    animationFrameRef.current = requestAnimationFrame(measurePerformance)
  }

  useEffect(() => {
    if (isVisible) {
      animationFrameRef.current = requestAnimationFrame(measurePerformance)
    } else {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [isVisible])

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed top-4 right-4 z-[10000] bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
      >
        Show Performance Metrics
      </button>
    )
  }

  return (
    <div className="fixed top-4 right-4 z-[10000] bg-black/80 backdrop-blur-md text-white p-4 rounded-lg text-sm font-mono border border-white/20">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-semibold">Cursor Performance</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white transition-colors"
        >
          ×
        </button>
      </div>
      
      <div className="space-y-1">
        <div className="flex justify-between">
          <span>FPS:</span>
          <span className={metrics.fps >= 55 ? 'text-green-400' : metrics.fps >= 30 ? 'text-yellow-400' : 'text-red-400'}>
            {metrics.fps}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Frame Time:</span>
          <span className={metrics.frameTime <= 16.67 ? 'text-green-400' : metrics.frameTime <= 33.33 ? 'text-yellow-400' : 'text-red-400'}>
            {metrics.frameTime}ms
          </span>
        </div>
        
        {metrics.memoryUsage > 0 && (
          <div className="flex justify-between">
            <span>Memory:</span>
            <span className={metrics.memoryUsage <= 50 ? 'text-green-400' : metrics.memoryUsage <= 100 ? 'text-yellow-400' : 'text-red-400'}>
              {metrics.memoryUsage}MB
            </span>
          </div>
        )}
      </div>
      
      <div className="mt-2 pt-2 border-t border-white/20 text-xs text-gray-400">
        Target: 60 FPS (16.67ms)
      </div>
    </div>
  )
}
