# Email Setup Guide for Contact Form

This guide will help you set up the email functionality for your portfolio contact form. The system supports multiple email delivery methods for maximum reliability.

## 🚀 Quick Setup (Recommended: EmailJS)

EmailJS is the easiest and most reliable method for client-side email delivery.

### Step 1: Create EmailJS Account
1. Go to [EmailJS.com](https://www.emailjs.com/)
2. Sign up for a free account
3. Verify your email address

### Step 2: Create Email Service
1. In your EmailJS dashboard, go to "Email Services"
2. Click "Add New Service"
3. Choose "Gmail" (recommended) or your preferred email provider
4. Connect your Gmail account (<EMAIL>)
5. Note down the **Service ID**

### Step 3: Create Email Template
1. Go to "Email Templates" in your dashboard
2. Click "Create New Template"
3. Use this template content:

```
Subject: Portfolio Contact: {{subject}}

From: {{from_name}} <{{from_email}}>
Subject: {{subject}}

Message:
{{message}}

---
This email was sent from your portfolio contact form.
Reply to: {{from_email}}
```

4. Note down the **Template ID**

### Step 4: Get Public Key
1. Go to "Account" → "General"
2. Copy your **Public Key**

### Step 5: Configure Environment Variables
1. Copy `.env.example` to `.env.local`
2. Fill in your EmailJS credentials:

```env
NEXT_PUBLIC_EMAILJS_SERVICE_ID=your_service_id_here
NEXT_PUBLIC_EMAILJS_TEMPLATE_ID=your_template_id_here
NEXT_PUBLIC_EMAILJS_PUBLIC_KEY=your_public_key_here
```

## 🔧 Alternative Setup (SMTP with Nodemailer)

For server-side email delivery using Gmail SMTP:

### Step 1: Enable 2-Factor Authentication
1. Go to your Google Account settings
2. Enable 2-Factor Authentication

### Step 2: Generate App Password
1. Go to Google Account → Security → 2-Step Verification
2. Scroll down to "App passwords"
3. Generate a new app password for "Mail"
4. Copy the 16-character password

### Step 3: Configure Environment Variables
Add to your `.env.local`:

```env
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_16_character_app_password
```

## 📧 Email Delivery Flow

The contact form uses a multi-layered approach:

1. **Primary**: EmailJS (client-side, most reliable)
2. **Fallback**: API route with Nodemailer (server-side)
3. **Final Fallback**: Direct mailto link

## ✅ Testing the Setup

1. Start your development server: `npm run dev`
2. Navigate to the contact section
3. Fill out and submit the form
4. Check for success/error messages
5. Verify email <NAME_EMAIL>

## 🔍 Troubleshooting

### EmailJS Issues
- Check browser console for errors
- Verify all environment variables are set
- Ensure EmailJS service is active
- Check EmailJS dashboard for delivery logs

### SMTP Issues
- Verify Gmail app password is correct
- Check if 2FA is enabled
- Ensure EMAIL_USER and EMAIL_PASS are in .env.local
- Check server logs for detailed error messages

### General Issues
- Ensure .env.local is not committed to git
- Restart development server after changing environment variables
- Check network connectivity
- Verify email addresses are valid

## 🛡️ Security Notes

- Never commit .env.local to version control
- Use app passwords, not your actual Gmail password
- EmailJS public key is safe to expose (it's meant to be public)
- Consider rate limiting for production deployment

## 📱 Features Included

- ✅ Real-time form validation
- ✅ Loading states and animations
- ✅ Success/error notifications with toast messages
- ✅ Fallback email client integration
- ✅ Mobile-responsive design
- ✅ Accessibility features
- ✅ Multiple delivery methods for reliability

## 🚀 Production Deployment

For production deployment:

1. Set environment variables in your hosting platform
2. Consider using a dedicated email service (SendGrid, Mailgun, etc.)
3. Implement rate limiting
4. Add CAPTCHA for spam protection
5. Monitor email delivery rates

Your contact form is now ready to receive messages reliably!
