# 📧 Gmail SMTP Setup Guide for Real-time Email Delivery

Your contact form is ready! Follow these steps to enable real-time email delivery to `<EMAIL>`.

## 🚀 Quick Setup Steps

### Step 1: Enable 2-Factor Authentication
1. Go to [Google Account Security](https://myaccount.google.com/security)
2. Sign in with your Gmail account (`<EMAIL>`)
3. Under "Signing in to Google", click **"2-Step Verification"**
4. Follow the prompts to enable 2FA (you'll need your phone)

### Step 2: Generate App Password
1. After enabling 2FA, go back to [Google Account Security](https://myaccount.google.com/security)
2. Under "Signing in to Google", click **"2-Step Verification"**
3. Scroll down to **"App passwords"** section
4. Click **"App passwords"**
5. Select **"Mail"** from the dropdown
6. Click **"Generate"**
7. **Copy the 16-character password** (it looks like: `abcd efgh ijkl mnop`)

### Step 3: Update Environment Variables
1. Open your `.env.local` file
2. Replace `your_gmail_app_password_here` with your actual app password:

```env
EMAIL_USER=<EMAIL>
EMAIL_PASS=abcd efgh ijkl mnop
```

**Important**: Remove the spaces from the app password, so it becomes: `abcdefghijklmnop`

### Step 4: Restart Your Server
```bash
npm run dev
```

## ✅ Test the Setup

1. Go to your contact form
2. Fill out a test message
3. Submit the form
4. Check the console logs for success messages
5. Check your email inbox for the message

## 🔍 Expected Console Output (Success)

When working correctly, you should see:
```
Attempting to send email via SMTP...
Creating SMTP transporter for: <EMAIL>
SMTP connection verified successfully
✅ Email sent successfully!
Message ID: <some-message-id>
```

## 🚨 Troubleshooting

### Error: "Invalid login"
- **Cause**: Wrong app password or 2FA not enabled
- **Solution**: 
  1. Verify 2FA is enabled
  2. Generate a new app password
  3. Update `.env.local` with the new password
  4. Restart the server

### Error: "SMTP credentials not configured"
- **Cause**: Environment variables not set
- **Solution**: 
  1. Check `.env.local` exists in your project root
  2. Verify `EMAIL_USER` and `EMAIL_PASS` are set
  3. Restart the server

### Error: "Connection timeout"
- **Cause**: Network or firewall issues
- **Solution**: 
  1. Check your internet connection
  2. Try from a different network
  3. Contact your ISP if the issue persists

### No errors but no email received
- **Cause**: Email might be in spam folder
- **Solution**: 
  1. Check your spam/junk folder
  2. Add your own email to safe senders
  3. Check Gmail's "All Mail" folder

## 📧 Email Format You'll Receive

```
Subject: Portfolio Contact: [Subject from form]

Hello Zaid,

You have received a new message from your portfolio contact form.

From: [Sender Name]
Email: [Sender Email]
Subject: [Message Subject]

Message:
[Message Content]

---
This email was sent from your portfolio contact form.
Timestamp: [Date and Time]
```

## 🔒 Security Notes

- ✅ App passwords are safer than your main Gmail password
- ✅ App passwords can be revoked anytime from Google Account settings
- ✅ Never share your app password publicly
- ✅ The `.env.local` file is already in `.gitignore` (won't be committed to Git)

## 🎉 Success!

Once configured, your contact form will:
- ✅ Send real-time emails to your inbox
- ✅ Include all form details (name, email, subject, message)
- ✅ Allow direct reply to the sender
- ✅ Provide professional email formatting
- ✅ Log all activity for debugging

Your portfolio is now ready to receive messages from potential employers and clients! 🚀
